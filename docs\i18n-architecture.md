# Keybr项目国际化(i18n)技术文档

## 1. 项目概述

Keybr是一个单词记忆与英语肌肉记忆锻炼软件，基于Vike + React + TypeScript技术栈。本文档描述了项目的国际化架构设计、实现方案和后续发展计划。

## 2. 技术架构设计

### 2.1 核心技术栈

- **i18n框架**: react-i18next + i18next
- **后端加载**: i18next-http-backend
- **状态管理**: Jotai (全局语言状态)
- **SSR支持**: Vike框架集成
- **类型系统**: TypeScript完整支持

### 2.2 架构图

```mermaid
graph TB
    A[用户界面] --> B[LanguageSwitcher组件]
    B --> C[Jotai语言状态]
    C --> D[i18next实例]
    D --> E[HTTP Backend]
    E --> F[public/locales翻译文件]
    
    G[Vike SSR] --> H[服务端语言检测]
    H --> D
    
    I[React组件] --> J[useTranslation Hook]
    J --> D
    
    K[自定义Hook] --> L[useI18n]
    L --> C
    L --> D
```

### 2.3 文件架构

```
src/
├── i18n/
│   └── index.ts              # 主配置文件
├── store/
│   └── languageAtom.ts       # Jotai语言状态管理
├── hooks/
│   └── useI18n.ts           # 自定义i18n Hook
├── components/
│   ├── LanguageSwitcher.tsx # 语言切换组件
│   └── I18nDebugger.tsx     # 开发调试组件
├── renderer/
│   ├── +onRenderHtml.tsx    # SSR语言初始化
│   └── +onRenderClient.tsx  # 客户端语言同步
└── pages/
    └── +onHydrationEnd.ts   # 水合后初始化

public/
└── locales/                 # 翻译资源文件
    ├── zh/                  # 中文翻译
    │   ├── common.json      # 通用翻译
    │   ├── typing.json      # 打字练习
    │   ├── article.json     # 文章练习
    │   ├── gallery.json     # 词典展示
    │   └── errors.json      # 错误信息
    └── en/                  # 英文翻译
        ├── common.json
        ├── typing.json
        ├── article.json
        ├── gallery.json
        └── errors.json
```

## 3. 核心实现方案

### 3.1 i18n配置 (`src/i18n/index.ts`)

```typescript
export const initI18n = async (initialLanguage: SupportedLanguage = "zh") => {
  await i18n
    .use(Backend)
    .use(initReactI18next)
    .init({
      lng: initialLanguage,
      fallbackLng: "zh",
      ns: ["common", "typing", "article", "gallery", "errors"],
      defaultNS: "common",
      backend: {
        loadPath: "/locales/{{lng}}/{{ns}}.json",
        requestOptions: { cache: 'default' },
      },
      react: { useSuspense: false },
      preload: ['zh', 'en'],
    });
};
```

### 3.2 状态管理 (`src/store/languageAtom.ts`)

```typescript
export const currentLanguageAtom = atomWithStorage<SupportedLanguage>(
  "currentLanguage",
  "zh"
);

export const detectBrowserLanguage = (): SupportedLanguage => {
  if (typeof window === "undefined") return "zh";
  const browserLang = navigator.language.toLowerCase();
  return browserLang.startsWith("zh") ? "zh" : 
         browserLang.startsWith("en") ? "en" : "zh";
};
```

### 3.3 SSR集成

**服务端渲染** (`src/renderer/+onRenderHtml.tsx`):
```typescript
const detectedLanguage = detectLanguageFromContext(pageContext);
await initI18n(detectedLanguage);
const htmlLang = detectedLanguage === "zh" ? "zh-CN" : "en";
```

**客户端水合** (`src/renderer/+onRenderClient.tsx`):
```typescript
const browserLanguage = detectBrowserLanguage();
await initI18n(browserLanguage);
```

### 3.4 组件使用模式

**标准用法**:
```typescript
import { useTranslation } from "react-i18next";

function MyComponent() {
  const { t } = useTranslation("common");
  return <h1>{t("navigation.home")}</h1>;
}
```

**多命名空间用法**:
```typescript
const { t } = useTranslation("article");
const { t: tCommon } = useTranslation("common");
```

**插值用法**:
```typescript
{t("practice.wordProgress", { current: 1, total: 100 })}
```

## 4. 当前实现状态

### 4.1 已完成页面

| 页面 | 路由 | 完成度 | 翻译键数量 | 备注 |
|------|------|--------|------------|------|
| 测试页面 | `/i18n-test` | 100% | 20+ | 功能演示和调试 |
| 文章练习 | `/CustomArticle` | 100% | 80+ | 完整流程国际化 |

### 4.2 翻译资源统计

| 命名空间 | 中文键数 | 英文键数 | 主要内容 |
|----------|----------|----------|----------|
| common.json | 45+ | 45+ | 导航、按钮、状态、时间 |
| article.json | 60+ | 60+ | 文章练习完整流程 |
| typing.json | 30+ | 30+ | 打字练习(待实现) |
| gallery.json | 25+ | 25+ | 词典展示(待实现) |
| errors.json | 20+ | 20+ | 错误信息 |

### 4.3 技术特性

- ✅ **SSR一致性**: 服务端和客户端语言状态同步
- ✅ **无刷新切换**: 基于Jotai的状态管理
- ✅ **按需加载**: HTTP backend动态加载翻译文件
- ✅ **缓存优化**: 浏览器缓存和预加载策略
- ✅ **SEO友好**: 动态HTML lang属性
- ✅ **开发调试**: I18nDebugger组件实时监控
- ✅ **类型安全**: TypeScript完整支持
- ✅ **插值支持**: 动态参数替换

## 5. 后续发展计划

### 5.1 短期计划 (1-2周)

#### 5.1.1 核心页面国际化
- **打字练习页面** (`/Typing`)
  - 优先级: 🔴 高
  - 预估工作量: 3-4天
  - 主要组件: WordPanel, ProgressBar, Settings, Results
  - 翻译键预估: 50+个

- **词典展示页面** (`/Gallery`)
  - 优先级: 🟡 中
  - 预估工作量: 2-3天
  - 主要组件: DictionaryList, FilterPanel, SearchBox
  - 翻译键预估: 30+个

#### 5.1.2 辅助页面国际化
- **更新日志页面** (`/updates`)
- **友情链接页面** (`/FriendLinks`)
- **用户认证相关页面**

### 5.2 中期计划 (2-4周)

#### 5.2.1 功能增强
- **语言自动检测优化**
  ```typescript
  // 基于用户地理位置的智能检测
  const detectUserLanguage = async () => {
    const geoLang = await getGeolocationLanguage();
    const browserLang = detectBrowserLanguage();
    return geoLang || browserLang || 'zh';
  };
  ```

- **翻译文件分割优化**
  ```
  public/locales/zh/
  ├── common/
  │   ├── navigation.json
  │   ├── buttons.json
  │   └── status.json
  ├── pages/
  │   ├── typing.json
  │   └── article.json
  └── components/
      ├── modals.json
      └── forms.json
  ```

- **翻译缓存策略**
  - 实现翻译文件版本控制
  - 增量更新机制
  - 离线翻译支持

#### 5.2.2 开发工具增强
- **翻译键管理工具**
  ```bash
  npm run i18n:extract    # 提取未翻译的键
  npm run i18n:validate   # 验证翻译完整性
  npm run i18n:unused     # 检查未使用的键
  ```

- **翻译质量检查**
  - 自动检测缺失翻译
  - 翻译键命名规范检查
  - 插值参数一致性验证

### 5.3 长期计划 (1-3个月)

#### 5.3.1 多语言扩展
- **新增语言支持**
  - 日语 (ja)
  - 韩语 (ko)
  - 德语 (de)
  - 法语 (fr)

- **RTL语言支持**
  - 阿拉伯语 (ar)
  - 希伯来语 (he)
  - CSS方向性适配

#### 5.3.2 高级特性
- **上下文感知翻译**
  ```typescript
  // 根据用户角色和场景提供不同翻译
  const { t } = useContextualTranslation({
    userRole: 'student',
    practiceLevel: 'beginner'
  });
  ```

- **动态翻译加载**
  ```typescript
  // 基于路由的翻译文件懒加载
  const { t } = useRouteTranslation('/typing');
  ```

- **翻译A/B测试**
  - 不同翻译版本的效果测试
  - 用户偏好数据收集

#### 5.3.3 性能优化
- **翻译文件CDN分发**
- **Service Worker缓存策略**
- **翻译文件压缩和合并**
- **Tree-shaking未使用翻译**

### 5.4 技术债务处理

#### 5.4.1 代码质量提升
- **翻译键类型定义**
  ```typescript
  type TranslationKeys = {
    'common.navigation.home': string;
    'article.practice.start': string;
    // 自动生成所有翻译键类型
  };
  ```

- **组件翻译测试**
  ```typescript
  // 每个组件的翻译覆盖率测试
  describe('ArticleHistory i18n', () => {
    it('should render all texts in Chinese', () => {
      // 测试中文翻译
    });
    it('should render all texts in English', () => {
      // 测试英文翻译
    });
  });
  ```

#### 5.4.2 文档完善
- **翻译贡献指南**
- **组件国际化最佳实践**
- **常见问题解决方案**

## 6. 风险评估与应对

### 6.1 技术风险
- **SSR水合不一致**: 已通过统一初始化流程解决
- **翻译文件加载失败**: 实现fallback机制和错误处理
- **性能影响**: 通过缓存和预加载优化

### 6.2 维护风险
- **翻译质量控制**: 建立翻译审核流程
- **版本同步问题**: 实现自动化翻译键管理
- **多人协作冲突**: 制定翻译文件修改规范

## 7. 成功指标

### 7.1 技术指标
- 翻译覆盖率: 目标100%
- 页面加载性能: 翻译加载时间<100ms
- 内存使用: 翻译数据占用<1MB

### 7.2 用户体验指标
- 语言切换响应时间: <200ms
- 翻译准确性: 用户满意度>95%
- 多语言用户增长: 目标20%

## 8. 结论

当前i18n架构采用了业界标准的react-i18next方案，与Vike框架深度集成，实现了完整的SSR支持和优秀的用户体验。通过合理的文件组织、状态管理和性能优化，为项目的国际化发展奠定了坚实基础。

后续将按照既定计划逐步完善剩余页面的国际化，并持续优化技术架构，最终实现全面的多语言支持，为全球用户提供优质的打字练习体验。

---

*文档版本: v1.0*
*最后更新: 2024年*
*维护者: AI Assistant*
