# Keybr国际化使用指南

## 快速开始

### 在组件中使用翻译

```typescript
import { useTranslation } from "react-i18next";

function MyComponent() {
  const { t } = useTranslation("common");
  
  return (
    <div>
      <h1>{t("navigation.home")}</h1>
      <button>{t("buttons.start")}</button>
    </div>
  );
}
```

### 多命名空间使用

```typescript
function ArticleComponent() {
  const { t } = useTranslation("article");
  const { t: tCommon } = useTranslation("common");
  
  return (
    <div>
      <h1>{t("practice.title")}</h1>
      <button>{tCommon("buttons.back")}</button>
    </div>
  );
}
```

### 插值使用

```typescript
// 翻译文件中: "welcome": "欢迎, {{name}}!"
<p>{t("welcome", { name: "张三" })}</p>

// 翻译文件中: "progress": "进度: {{current}}/{{total}}"
<span>{t("progress", { current: 5, total: 10 })}</span>
```

## 语言切换

### 使用语言切换组件

```typescript
import { LanguageSwitcher, LanguageSwitcherCompact } from "@/components/LanguageSwitcher";

// 完整版语言切换器
<LanguageSwitcher showLabel={true} />

// 紧凑版语言切换器（只显示图标）
<LanguageSwitcherCompact />
```

### 编程方式切换语言

```typescript
import { useI18n } from "@/hooks/useI18n";

function MyComponent() {
  const { switchLanguage, toggleLanguage, currentLanguage } = useI18n();
  
  const handleSwitchToEnglish = () => {
    switchLanguage('en');
  };
  
  const handleToggle = () => {
    toggleLanguage(); // 在中英文之间切换
  };
  
  return (
    <div>
      <p>当前语言: {currentLanguage}</p>
      <button onClick={handleSwitchToEnglish}>切换到英文</button>
      <button onClick={handleToggle}>切换语言</button>
    </div>
  );
}
```

## 翻译文件结构

### 命名空间说明

- `common`: 通用文本（导航、按钮、状态等）
- `typing`: 打字练习页面
- `article`: 文章练习页面
- `gallery`: 词典展示页面
- `errors`: 错误信息

### 翻译键命名规范

```json
{
  "section": {
    "subsection": {
      "key": "翻译文本"
    }
  }
}
```

示例：
```json
{
  "navigation": {
    "home": "首页",
    "typing": "打字练习"
  },
  "buttons": {
    "start": "开始",
    "stop": "停止"
  }
}
```

## 添加新翻译

### 1. 在翻译文件中添加键值对

**中文** (`public/locales/zh/common.json`):
```json
{
  "newSection": {
    "newKey": "新的中文文本"
  }
}
```

**英文** (`public/locales/en/common.json`):
```json
{
  "newSection": {
    "newKey": "New English Text"
  }
}
```

### 2. 在组件中使用

```typescript
const { t } = useTranslation("common");
<span>{t("newSection.newKey")}</span>
```

## 最佳实践

### 1. 翻译键命名

- 使用小写字母和点号分隔
- 按功能模块组织
- 保持键名简洁明了

```json
// ✅ 好的命名
{
  "user": {
    "profile": {
      "name": "姓名",
      "email": "邮箱"
    }
  }
}

// ❌ 不好的命名
{
  "UserProfileNameLabel": "姓名",
  "user_profile_email_text": "邮箱"
}
```

### 2. 插值使用

```json
// ✅ 使用插值
{
  "welcome": "欢迎, {{name}}!",
  "progress": "进度: {{current}}/{{total}}"
}

// ❌ 硬编码动态内容
{
  "welcomeJohn": "欢迎, John!",
  "progress50": "进度: 5/10"
}
```

### 3. 错误处理

```typescript
const { t } = useTranslation("errors");

try {
  // 一些操作
} catch (error) {
  showError(t("network.connectionFailed"));
}
```

## 调试工具

### 开发环境调试器

在开发环境中，可以使用 `I18nDebugger` 组件来监控i18n状态：

```typescript
import { I18nDebugger } from "@/components/I18nDebugger";

// 在页面中添加调试器（仅开发环境显示）
{process.env.NODE_ENV === "development" && <I18nDebugger />}
```

调试器会显示：
- 当前语言状态
- 翻译文件加载状态
- 可用的命名空间
- 翻译测试结果

### 浏览器控制台

在浏览器控制台中可以查看i18n相关日志：
- 语言切换日志
- 翻译文件加载日志
- 错误信息

## 常见问题

### Q: 翻译不生效怎么办？

1. 检查翻译键是否正确
2. 确认翻译文件是否存在
3. 查看浏览器网络请求是否成功加载翻译文件
4. 使用 `I18nDebugger` 检查状态

### Q: 如何处理复数形式？

```json
{
  "item": "项目",
  "item_plural": "项目"
}
```

### Q: 如何处理长文本？

对于长文本，建议拆分为多个短句：

```json
{
  "guide": {
    "step1": "第一步：添加文本",
    "step1Desc": "在文本框中输入或粘贴您想要练习的内容。",
    "step2": "第二步：开始练习",
    "step2Desc": "点击开始按钮进行练习。"
  }
}
```


