{"title": "Article Practice", "subtitle": "Improve typing skills through article practice", "history": {"title": "Article List", "userArticles": "Custom Articles", "officialArticles": "Official Articles", "empty": "No articles yet", "loadMore": "Load More", "delete": "Delete Article", "edit": "Edit Article", "emptyUser": "No saved custom articles", "emptyUserDesc": "Start creating a new article to practice!", "emptyOfficial": "No official articles", "emptyOfficialDesc": "Try saving official articles from recommendations", "loadError": "Failed to load articles", "deleteConfirm": "Are you sure you want to delete this article?", "deleteError": "Failed to delete article, please try again.", "deleteTooltipOfficial": "Official articles cannot be deleted", "deleteTooltip": "Delete article"}, "upload": {"title": "Upload Custom Article", "descStep1": "Enter the article title and content you want to practice, up to {{maxChars}} characters", "descStep2": "Adjust text settings for the best practice experience", "errorTitleEmpty": "Please enter the article title", "errorContentEmpty": "Please enter the article content", "saveError": "Failed to save the article, please try again.", "saveErrorLog": "Failed to save article"}, "editor": {"title": "Article Editor", "articleTitle": "Article Title", "articleContent": "Article Content", "placeholder": {"title": "Enter article title", "content": "Enter article content"}, "save": "Save Article", "cancel": "Cancel Edit", "preview": "Preview Article", "articleTitlePlaceholder": "Please enter the article title", "articleContentPlaceholder": "Please enter or paste the article content..."}, "input": {"title": "Add Text", "description": "Enter or paste the text content you want to practice, up to {{maxChars}} characters.", "label": "Text Content", "placeholder": "Enter or paste text here...", "clear": "Clear", "next": "Next", "errorEmpty": "Please enter text content", "errorTooLong": "Character limit exceeded ({{count}}/{{maxChars}})"}, "preprocess": {"title": "Text Preprocessing", "description": "Adjust text settings for the best practice experience.", "removePunctuation": "Remove Punctuation", "removePunctuationDesc": "Remove punctuation from text to focus on word training and improve typing efficiency", "enableSound": "Play Word Pronunciation", "enableSoundDesc": "Disabled by default. Not recommended for fast practice as pronunciation may not load in time", "preview": "Preview", "wordCount": "Word count: {{count}}", "back": "Back", "startPractice": "Start Practice"}, "practice": {"title": "Article Practice", "startPractice": "Start Practice", "pausePractice": "Pause Practice", "resumePractice": "Resume Practice", "finishPractice": "Finish Practice", "progress": "Practice Progress", "currentParagraph": "Current Paragraph", "totalParagraphs": "Total Paragraphs", "inputMethodWarning": "You are using an input method, please turn it off.", "pressAnyKey": "Press any key to start practice", "completed": "Practice Completed!", "customArticle": "Custom Article", "source": "Source: {{title}}", "wordProgress": "Word: {{current}} / {{total}}", "speed": "Speed: {{speed}} WPM", "accuracy": "Accuracy: {{accuracy}}%", "time": "Time: {{time}}s", "pause": "Pause", "resume": "Resume", "restart": "<PERSON><PERSON>", "backToList": "Back to Article List", "backToCurrentPosition": "Back to Current Position", "practiceAgain": "Practice Again", "articleLibrary": "Article Library", "personalArticles": "Personal Articles", "uploadArticle": "Upload Article", "timeLabel": "Time", "speedLabel": "Speed", "accuracyLabel": "Accuracy", "errorsLabel": "Errors", "seconds": "seconds"}, "results": {"title": "Practice Results", "articleTitle": "Article Title", "practiceTime": "Practice Time", "typingSpeed": "Typing <PERSON>", "accuracy": "Accuracy", "errorCount": "Error Count", "share": "Share Results", "practiceAgain": "Practice Again", "backToList": "Back to List"}, "guide": {"title": "User Guide", "customArticleTitle": "Custom Article Feature User Guide", "introduction": "Feature Introduction", "introText": "The custom article feature allows you to practice with your own text content, enabling you to practice professional terminology from work, study materials, or any text content you're interested in.", "features": "Main Features", "feature1": "Fully Local", "feature1Desc": "Data saved in browser, no cross-device support", "feature2": "Flexible Input", "feature2Desc": "Supports up to 3000 characters", "feature3": "Smart Processing", "feature3Desc": "Option to remove punctuation", "feature4": "Sound Feedback", "feature4Desc": "Enable key sound effects", "feature5": "Easy Save", "feature5Desc": "Save frequently used articles", "feature6": "Real-time Stats", "feature6Desc": "Display speed and accuracy", "steps": "Usage Steps", "step1": "Step 1: Add Text", "step1Desc": "Enter or paste the content you want to practice in the text box (up to 3000 characters), which can be articles, code, professional terms, or any text.", "step2": "Step 2: Text Preprocessing", "step2Desc": "Choose whether to remove punctuation, enable sound feedback, and preview the processing effect. Click \"Save Article\" if you need repeated practice.", "step3": "Step 3: Start Practice", "step3Desc": "Practice word by word with real-time display of speed and accuracy. Supports pause, continue, restart and other operations.", "faq": "FAQ", "faq1": "Will my article data be lost?", "faq1Answer": "Article data is saved in the browser's local storage and will not be lost as long as you don't clear browser data. Switching browsers or accessing from other devices will result in data loss.", "faq2": "Can I practice code?", "faq2Answer": "Yes! It's recommended to enable the \"Remove Punctuation\" option to focus on practicing keywords in code.", "faq3": "Will cloud saving be supported in the future?", "faq3Answer": "Cloud saving may be supported in future versions, stay tuned.", "tips": "Tips", "tipsText": "The custom article feature is particularly suitable for practicing terminology in professional fields, preparing exam materials, or improving proficiency with specific content. Practice is case-insensitive, and input errors will automatically reset, allowing you to focus on improving typing speed and accuracy."}}