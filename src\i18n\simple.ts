import type { SupportedLanguage } from "@/store/languageAtom";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// 简化的翻译资源
const simpleResources = {
  zh: {
    common: {
      "navigation.home": "首页",
      "navigation.typing": "打字练习",
      "navigation.article": "文章练习",
      "buttons.start": "开始",
      "buttons.stop": "停止",
      "buttons.back": "返回",
      "buttons.retry": "重试",
      "buttons.practice": "练习",
      "buttons.delete": "删除",
      "buttons.create": "创建新文章",
      "language.switch": "切换语言",
      "status.loading": "加载中...",
      "status.official": "官方",
      "time.created": "创建时间",
      "time.lastPracticed": "上次练习",
      "sort.label": "排序",
      "sort.lastPracticed": "最近练习",
      "sort.created": "创建时间",
    },
    article: {
      "history.title": "文章列表",
      "history.userArticles": "自定义文章",
      "history.officialArticles": "官方文章",
      "history.empty": "暂无文章",
      "history.loadMore": "加载更多",
      "history.delete": "删除文章",
      "history.edit": "编辑文章",
      "history.emptyUser": "暂无保存的自定义文章",
      "history.emptyUserDesc": "开始创建一篇新的文章进行练习吧！",
      "history.emptyOfficial": "暂无官方文章",
      "history.emptyOfficialDesc": "请尝试在推荐文章中保存官方文章",
      "history.loadError": "加载文章失败",
      "history.deleteConfirm": "确定要删除这篇文章吗？",
      "history.deleteError": "删除文章失败，请重试。",
      "history.deleteTooltipOfficial": "官方文章不可删除",
      "history.deleteTooltip": "删除文章",
      "practice.title": "文章练习",
      "practice.startPractice": "开始练习",
      "editor.title": "文章编辑",
      "editor.articleTitle": "文章标题",
      "editor.articleContent": "文章内容",
      "guide.title": "使用指南",
      "guide.customArticleTitle": "自定义文章功能使用指南",
      "guide.introduction": "功能简介",
      "guide.introText":
        "自定义文章功能允许您使用自己的文本内容进行练习，让您可以练习工作中常用的专业术语、学习材料或任何您感兴趣的文本内容。",
      "guide.features": "主要特点",
      "guide.feature1": "完全本地化",
      "guide.feature1Desc": "数据保存在浏览器中，不支持跨设备",
      "guide.feature2": "灵活输入",
      "guide.feature2Desc": "支持最多3000字符",
      "guide.feature3": "智能处理",
      "guide.feature3Desc": "可选择移除标点符号",
      "guide.feature4": "声音反馈",
      "guide.feature4Desc": "可启用按键音效",
      "guide.feature5": "便捷保存",
      "guide.feature5Desc": "保存常用文章",
      "guide.feature6": "实时统计",
      "guide.feature6Desc": "显示速度和准确率",
      "guide.steps": "使用步骤",
      "guide.step1": "第一步：添加文本",
      "guide.step1Desc":
        "在文本框中输入或粘贴您想要练习的内容（最多3000字符），可以是文章、代码、专业术语等任何文本。",
      "guide.step2": "第二步：文本预处理",
      "guide.step2Desc":
        '选择是否移除标点符号、启用声音反馈，预览处理效果。如需重复练习，可点击"保存文章"。',
      "guide.step3": "第三步：开始练习",
      "guide.step3Desc":
        "按单词进行练习，实时显示速度、准确率。支持暂停、继续、重新开始等操作。",
      "guide.faq": "常见问题",
      "guide.faq1": "我的文章数据会丢失吗？",
      "guide.faq1Answer":
        "文章数据保存在浏览器的本地存储中，只要不清除浏览器数据就不会丢失。切换浏览器、其他设备访问会丢失。",
      "guide.faq2": "可以练习代码吗？",
      "guide.faq2Answer":
        '可以！建议开启"移除标点符号"选项来专注于代码中的关键词练习。',
      "guide.faq3": "后续可以支持云端保存吗？",
      "guide.faq3Answer": "可能会在后续版本中支持云端保存，敬请期待。",
      "guide.tips": "小贴士",
      "guide.tipsText":
        "自定义文章功能特别适合练习专业领域的术语、准备考试材料或提高特定内容的熟练度。练习时不区分大小写，输入错误会自动重置，让您专注于提高打字速度和准确性。",
    },
  },
  en: {
    common: {
      "navigation.home": "Home",
      "navigation.typing": "Typing Practice",
      "navigation.article": "Article Practice",
      "buttons.start": "Start",
      "buttons.stop": "Stop",
      "buttons.back": "Back",
      "buttons.retry": "Retry",
      "buttons.practice": "Practice",
      "buttons.delete": "Delete",
      "buttons.create": "Create New Article",
      "language.switch": "Switch Language",
      "status.loading": "Loading...",
      "status.official": "Official",
      "time.created": "Created",
      "time.lastPracticed": "Last Practiced",
      "sort.label": "Sort",
      "sort.lastPracticed": "Last Practiced",
      "sort.created": "Created Time",
    },
    article: {
      "history.title": "Article List",
      "history.userArticles": "Custom Articles",
      "history.officialArticles": "Official Articles",
      "history.empty": "No articles yet",
      "history.loadMore": "Load More",
      "history.delete": "Delete Article",
      "history.edit": "Edit Article",
      "history.emptyUser": "No saved custom articles",
      "history.emptyUserDesc": "Start creating a new article to practice!",
      "history.emptyOfficial": "No official articles",
      "history.emptyOfficialDesc":
        "Try saving official articles from recommendations",
      "history.loadError": "Failed to load articles",
      "history.deleteConfirm": "Are you sure you want to delete this article?",
      "history.deleteError": "Failed to delete article, please try again.",
      "history.deleteTooltipOfficial": "Official articles cannot be deleted",
      "history.deleteTooltip": "Delete article",
      "practice.title": "Article Practice",
      "practice.startPractice": "Start Practice",
      "editor.title": "Article Editor",
      "editor.articleTitle": "Article Title",
      "editor.articleContent": "Article Content",
      "guide.title": "User Guide",
      "guide.customArticleTitle": "Custom Article Feature User Guide",
      "guide.introduction": "Feature Introduction",
      "guide.introText":
        "The custom article feature allows you to practice with your own text content, enabling you to practice professional terminology from work, study materials, or any text content you're interested in.",
      "guide.features": "Main Features",
      "guide.feature1": "Fully Local",
      "guide.feature1Desc": "Data saved in browser, no cross-device support",
      "guide.feature2": "Flexible Input",
      "guide.feature2Desc": "Supports up to 3000 characters",
      "guide.feature3": "Smart Processing",
      "guide.feature3Desc": "Option to remove punctuation",
      "guide.feature4": "Sound Feedback",
      "guide.feature4Desc": "Enable key sound effects",
      "guide.feature5": "Easy Save",
      "guide.feature5Desc": "Save frequently used articles",
      "guide.feature6": "Real-time Stats",
      "guide.feature6Desc": "Display speed and accuracy",
      "guide.steps": "Usage Steps",
      "guide.step1": "Step 1: Add Text",
      "guide.step1Desc":
        "Enter or paste the content you want to practice in the text box (up to 3000 characters), which can be articles, code, professional terms, or any text.",
      "guide.step2": "Step 2: Text Preprocessing",
      "guide.step2Desc":
        'Choose whether to remove punctuation, enable sound feedback, and preview the processing effect. Click "Save Article" if you need repeated practice.',
      "guide.step3": "Step 3: Start Practice",
      "guide.step3Desc":
        "Practice word by word with real-time display of speed and accuracy. Supports pause, continue, restart and other operations.",
      "guide.faq": "FAQ",
      "guide.faq1": "Will my article data be lost?",
      "guide.faq1Answer":
        "Article data is saved in the browser's local storage and will not be lost as long as you don't clear browser data. Switching browsers or accessing from other devices will result in data loss.",
      "guide.faq2": "Can I practice code?",
      "guide.faq2Answer":
        'Yes! It\'s recommended to enable the "Remove Punctuation" option to focus on practicing keywords in code.',
      "guide.faq3": "Will cloud saving be supported in the future?",
      "guide.faq3Answer":
        "Cloud saving may be supported in future versions, stay tuned.",
      "guide.tips": "Tips",
      "guide.tipsText":
        "The custom article feature is particularly suitable for practicing terminology in professional fields, preparing exam materials, or improving proficiency with specific content. Practice is case-insensitive, and input errors will automatically reset, allowing you to focus on improving typing speed and accuracy.",
    },
  },
};

let isSimpleI18nInitialized = false;

export const initSimpleI18n = async (
  initialLanguage: SupportedLanguage = "zh"
) => {
  if (isSimpleI18nInitialized) {
    if (i18n.language !== initialLanguage) {
      await i18n.changeLanguage(initialLanguage);
    }
    return i18n;
  }

  console.log("Initializing simple i18n with language:", initialLanguage);

  await i18n.use(initReactI18next).init({
    lng: initialLanguage,
    fallbackLng: "zh",
    debug: false,

    ns: ["common", "article"],
    defaultNS: "common",

    interpolation: {
      escapeValue: false,
    },

    react: {
      useSuspense: false,
    },

    resources: simpleResources,
  });

  isSimpleI18nInitialized = true;
  console.log("Simple i18n initialized successfully");
  return i18n;
};

export { i18n as simpleI18n };
